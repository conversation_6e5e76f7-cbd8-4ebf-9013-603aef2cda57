#!/bin/bash
# 构建和推送Docker镜像的脚本
# 注意：应用镜像现在使用supervisor管理所有进程

# 设置镜像仓库和标签
REGISTRY=${REGISTRY:-"registry.cn-hangzhou.aliyuncs.com/secloud"}
BASE_IMAGE_NAME=${BASE_IMAGE_NAME:-"config-converter-base"}
APP_IMAGE_NAME=${APP_IMAGE_NAME:-"config-converter"}
TAG=${TAG:-"2.9.1"}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${GREEN}构建配置转换服务Docker镜像${NC}"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -r, --registry NAME     设置镜像仓库地址 (默认: $REGISTRY)"
    echo "  -b, --base NAME         设置基础镜像名称 (默认: $BASE_IMAGE_NAME)"
    echo "  -a, --app NAME          设置应用镜像名称 (默认: $APP_IMAGE_NAME)"
    echo "  -t, --tag TAG           设置镜像标签 (默认: $TAG)"
    echo "  --base-only             只构建基础镜像"
    echo "  --app-only              只构建应用镜像"
    echo "  --no-push               构建后不推送镜像"
    echo "  --aliyun                直接使用阿里云仓库 registry.cn-hangzhou.aliyuncs.com/secloud/config-converter:2.9.1"
    echo ""
    echo "示例:"
    echo "  $0 --registry myregistry.com --tag v1.0.0"
    echo "  $0 --app-only           # 只构建和推送应用镜像"
    echo "  $0 --base-only --no-push # 只构建基础镜像但不推送"
    echo "  $0 --aliyun             # 使用阿里云仓库的默认设置"
}

# 参数解析
BUILD_BASE=true
BUILD_APP=true
PUSH_IMAGES=true

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -b|--base)
            BASE_IMAGE_NAME="$2"
            shift 2
            ;;
        -a|--app)
            APP_IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        --base-only)
            BUILD_BASE=true
            BUILD_APP=false
            shift
            ;;
        --app-only)
            BUILD_BASE=false
            BUILD_APP=true
            shift
            ;;
        --no-push)
            PUSH_IMAGES=false
            shift
            ;;
        --aliyun)
            REGISTRY="registry.cn-hangzhou.aliyuncs.com/secloud"
            BASE_IMAGE_NAME="config-converter-base"
            APP_IMAGE_NAME="config-converter"
            TAG="2.9.1"
            shift
            ;;
        *)
            echo -e "${RED}错误: 未知选项 $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 构建完整的镜像名称
BASE_IMAGE="${REGISTRY}/${BASE_IMAGE_NAME}:${TAG}"
APP_IMAGE="${REGISTRY}/${APP_IMAGE_NAME}:${TAG}"

# 构建基础镜像
build_base_image() {
    echo -e "${GREEN}开始构建基础镜像: ${BASE_IMAGE}${NC}"
    
    docker build -t ${BASE_IMAGE} -f Dockerfile.base .
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 基础镜像构建失败${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}基础镜像构建成功: ${BASE_IMAGE}${NC}"
    
    # 立即推送基础镜像，确保应用镜像构建时可以获取
    if [ "$PUSH_IMAGES" = true ]; then
        echo -e "${GREEN}推送基础镜像: ${BASE_IMAGE}${NC}"
        docker push ${BASE_IMAGE}
        if [ $? -ne 0 ]; then
            echo -e "${RED}警告: 基础镜像推送失败${NC}"
        else
            echo -e "${GREEN}基础镜像推送成功${NC}"
        fi
    else
        echo -e "${YELLOW}跳过推送基础镜像${NC}"
    fi
}

# 构建应用镜像
build_app_image() {
    echo -e "${GREEN}开始构建应用镜像: ${APP_IMAGE}${NC}"
    
    # 显式拉取基础镜像，确保使用最新的基础镜像
    echo -e "${YELLOW}拉取基础镜像: ${BASE_IMAGE}${NC}"
    docker pull ${BASE_IMAGE}
    
    # 确保supervisor.conf存在
    if [ ! -f "supervisor.conf" ]; then
        echo -e "${RED}错误: supervisor.conf不存在，应用镜像需要此文件${NC}"
        exit 1
    fi
    
    # 直接使用原始Dockerfile.app构建应用镜像
    echo -e "${YELLOW}使用原始Dockerfile.app构建应用镜像，确保FROM指令引用正确${NC}"
    docker build -t ${APP_IMAGE} -f Dockerfile.app .
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 应用镜像构建失败${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}应用镜像构建成功: ${APP_IMAGE}${NC}"
    
    # 验证镜像大小
    base_size=$(docker images ${BASE_IMAGE} --format "{{.Size}}")
    app_size=$(docker images ${APP_IMAGE} --format "{{.Size}}")
    echo -e "${YELLOW}基础镜像大小: ${base_size}${NC}"
    echo -e "${YELLOW}应用镜像大小: ${app_size}${NC}"
    
    # 验证层共享
    echo -e "${YELLOW}验证镜像层共享...${NC}"
    base_layers=$(docker inspect ${BASE_IMAGE} -f '{{.RootFS.Layers}}' | wc -w)
    app_layers=$(docker inspect ${APP_IMAGE} -f '{{.RootFS.Layers}}' | wc -w)
    echo -e "${YELLOW}基础镜像层数: ${base_layers}${NC}"
    echo -e "${YELLOW}应用镜像层数: ${app_layers}${NC}"
    app_new_layers=$((app_layers - base_layers))
    echo -e "${YELLOW}应用镜像新增层数: ${app_new_layers}${NC}"
    
    # 只有在确认构建成功后才推送应用镜像
    if [ "$PUSH_IMAGES" = true ]; then
        echo -e "${GREEN}推送应用镜像: ${APP_IMAGE}${NC}"
        docker push ${APP_IMAGE}
        if [ $? -ne 0 ]; then
            echo -e "${RED}警告: 应用镜像推送失败${NC}"
        else
            echo -e "${GREEN}应用镜像推送成功${NC}"
        fi
    else
        echo -e "${YELLOW}跳过推送应用镜像${NC}"
    fi
}

# 主流程
echo -e "${GREEN}开始构建Docker镜像...${NC}"
echo -e "${YELLOW}基础镜像: ${BASE_IMAGE}${NC}"
echo -e "${YELLOW}应用镜像: ${APP_IMAGE}${NC}"

# 先检查docker命令是否可用
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: docker命令不可用${NC}"
    exit 1
fi

# 构建镜像
if [ "$BUILD_BASE" = true ]; then
    build_base_image
fi

if [ "$BUILD_APP" = true ]; then
    build_app_image
fi

echo -e "${GREEN}构建完成!${NC}"
exit 0 