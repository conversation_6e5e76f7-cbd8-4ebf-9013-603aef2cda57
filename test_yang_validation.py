#!/usr/bin/env python3
"""
简单的YANG验证测试脚本
用于测试修复后的XML格式是否符合YANG模型要求
"""

import re
import sys
from lxml import etree

def test_xml_format_issues(xml_file):
    """
    测试XML格式问题
    
    Args:
        xml_file: XML文件路径
    """
    print(f"正在测试XML文件: {xml_file}")
    
    try:
        # 读取XML文件
        with open(xml_file, 'r', encoding='utf-8') as f:
            xml_content = f.read()
        
        # 解析XML
        root = etree.fromstring(xml_content.encode('utf-8'))
        
        issues = []
        
        # 1. 检查元素文本值是否包含换行符
        print("1. 检查元素文本值中的换行符...")
        newline_elements = []
        for elem in root.iter():
            if elem.text and '\n' in elem.text.strip():
                newline_elements.append(f"{elem.tag}: '{elem.text}'")
        
        if newline_elements:
            issues.append(f"发现 {len(newline_elements)} 个包含换行符的元素文本值")
            for i, elem in enumerate(newline_elements[:5]):  # 只显示前5个
                print(f"  - {elem}")
            if len(newline_elements) > 5:
                print(f"  ... 还有 {len(newline_elements) - 5} 个")
        else:
            print("  ✓ 没有发现包含换行符的元素文本值")
        
        # 2. 检查布尔值格式
        print("2. 检查布尔值格式...")
        boolean_issues = []
        for elem in root.iter():
            if elem.text and elem.text.strip().lower() in ['true', 'false']:
                if elem.text != elem.text.strip():
                    boolean_issues.append(f"{elem.tag}: '{elem.text}'")
        
        if boolean_issues:
            issues.append(f"发现 {len(boolean_issues)} 个格式不正确的布尔值")
            for issue in boolean_issues[:5]:
                print(f"  - {issue}")
        else:
            print("  ✓ 布尔值格式正确")
        
        # 3. 检查IP地址格式（接口配置）
        print("3. 检查接口IP地址格式...")
        interface_ip_issues = []
        for ip_elem in root.xpath(".//ip[text()]"):
            ip_text = ip_elem.text.strip()
            if '/' in ip_text:
                # 检查是否为CIDR格式
                if not re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/\d{1,2}$', ip_text):
                    interface_ip_issues.append(f"接口IP格式错误: '{ip_text}'")
        
        if interface_ip_issues:
            issues.append(f"发现 {len(interface_ip_issues)} 个接口IP格式问题")
            for issue in interface_ip_issues[:3]:
                print(f"  - {issue}")
        else:
            print("  ✓ 接口IP地址格式正确")
        
        # 4. 检查地址对象IP格式
        print("4. 检查地址对象IP格式...")
        address_ip_issues = []
        for ip_addr_elem in root.xpath(".//ip-address[text()]"):
            ip_text = ip_addr_elem.text.strip()
            # 检查CIDR格式或IP范围格式
            if not (re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/\d{1,2}$', ip_text) or
                    re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}-\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$', ip_text)):
                address_ip_issues.append(f"地址对象IP格式错误: '{ip_text}'")
        
        if address_ip_issues:
            issues.append(f"发现 {len(address_ip_issues)} 个地址对象IP格式问题")
            for issue in address_ip_issues[:3]:
                print(f"  - {issue}")
        else:
            print("  ✓ 地址对象IP格式正确")
        
        # 5. 检查接口名称格式
        print("5. 检查接口名称格式...")
        interface_name_issues = []
        for name_elem in root.xpath(".//interface/name[text()]"):
            name_text = name_elem.text.strip()
            if not re.match(r'^[-A-Za-z0-9._@/]+$', name_text):
                interface_name_issues.append(f"接口名称格式错误: '{name_text}'")
        
        if interface_name_issues:
            issues.append(f"发现 {len(interface_name_issues)} 个接口名称格式问题")
            for issue in interface_name_issues[:3]:
                print(f"  - {issue}")
        else:
            print("  ✓ 接口名称格式正确")
        
        # 总结
        print("\n" + "="*50)
        if issues:
            print(f"发现 {len(issues)} 类问题:")
            for i, issue in enumerate(issues, 1):
                print(f"{i}. {issue}")
            print("\n建议进一步修复这些问题以通过YANG验证。")
            return False
        else:
            print("✓ 所有检查项都通过！XML格式符合基本YANG要求。")
            return True
            
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python test_yang_validation.py <xml_file>")
        sys.exit(1)
    
    xml_file = sys.argv[1]
    success = test_xml_format_issues(xml_file)
    sys.exit(0 if success else 1)
