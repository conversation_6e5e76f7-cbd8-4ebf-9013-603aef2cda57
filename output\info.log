2025-08-05 09:38:48 - INFO - Log initialization complete
2025-08-05 09:38:48 - WARNING - 消息中缺少参数: language
2025-08-05 09:38:48 - INFO - 多厂商配置转换工具已启动，模式: convert, 厂商: fortigate, 语言: [缺失:language]
2025-08-05 09:38:48 - INFO - 开始转换配置文件
2025-08-05 09:38:48 - INFO - 目标型号: z3200s
2025-08-05 09:38:48 - INFO - 目标版本: R11
2025-08-05 09:38:48 - INFO - PhysicalInterfaceHandler初始化 - operation_mode_result为空
2025-08-05 09:38:48 - INFO - PhysicalInterfaceHandler初始化 - operation_mode_result为空
2025-08-05 09:38:48 - INFO - PhysicalInterfaceHandler初始化 - operation_mode_result为空
2025-08-05 09:38:48 - INFO - NTOS内置服务已加载: 82 个
2025-08-05 09:38:48 - INFO - 厂商映射已加载: fortigate，数量: 208
2025-08-05 09:38:48 - INFO - 服务映射已加载: 104 个
2025-08-05 09:38:48 - INFO - 服务别名已加载: 69 个
2025-08-05 09:38:48 - INFO - name_mapping_manager.initialized
2025-08-05 09:38:48 - INFO - name_mapping_manager.initialized
2025-08-05 09:38:48 - INFO - 转换前验证配置
2025-08-05 09:38:48 - INFO - 开始验证配置文件
2025-08-05 09:38:48 - INFO - 最低版本要求
2025-08-05 09:38:48 - INFO - 检测到FortiOS版本: 7.0.17
2025-08-05 09:38:48 - INFO - 配置文件验证通过
2025-08-05 09:38:48 - INFO - 验证通过
2025-08-05 09:38:48 - INFO - 使用新架构进行转换
2025-08-05 09:38:48 - INFO - 配置管理器：检测到开发环境
2025-08-05 09:38:48 - INFO - 配置管理器已初始化
2025-08-05 09:38:48 - INFO - 缓存管理器已初始化
2025-08-05 09:38:48 - INFO - 性能监控器已初始化
2025-08-05 09:38:48 - INFO - 缓存管理器：缓存已创建
2025-08-05 09:38:48 - INFO - 模板管理器已初始化
2025-08-05 09:38:48 - INFO - YANG管理器已初始化
2025-08-05 09:38:48 - INFO - 性能监控器已初始化
2025-08-05 09:38:48 - INFO - 内存管理器已初始化
2025-08-05 09:38:48 - INFO - 异常注册表已初始化
2025-08-05 09:38:48 - INFO - 错误处理器已初始化
2025-08-05 09:38:48 - INFO - 转换工作流程已初始化
2025-08-05 09:38:48 - INFO - 转换服务已初始化
2025-08-05 09:38:48 - INFO - 开始转换
2025-08-05 09:38:48 - INFO - 模板已找到
2025-08-05 09:38:48 - INFO - YANG模型已找到
2025-08-05 09:38:48 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-05 09:38:48 - WARNING - 消息中缺少参数: urn
2025-08-05 09:38:48 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-05 09:38:48 - WARNING - 消息中缺少参数: urn
2025-08-05 09:38:48 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-05 09:38:48 - WARNING - 消息中缺少参数: urn
2025-08-05 09:38:48 - WARNING - 命名空间不匹配: 实际='None', 期望='urn:ruijie:ntos'
2025-08-05 09:38:48 - INFO - 模板文件命名空间保持原样，不进行修改
2025-08-05 09:38:48 - INFO - 模板加载成功: 型号=z3200s, 版本=R11, 类型=running
2025-08-05 09:38:48 - INFO - 找到YANG文件
2025-08-05 09:38:48 - INFO - 模式已加载
2025-08-05 09:38:48 - INFO - YANG管理器：模式已加载
2025-08-05 09:38:48 - INFO - YANG管理器：命名空间已加载
2025-08-05 09:38:48 - INFO - name_mapping_manager.initialized
2025-08-05 09:38:48 - INFO - 管道管理器：已初始化
2025-08-05 09:38:48 - INFO - Interface mapping loaded
2025-08-05 09:38:48 - INFO - Interface mapping loaded (flat format): 7 mappings
2025-08-05 09:38:48 - INFO - 转换策略已初始化
2025-08-05 09:38:48 - INFO - Fortigate策略阶段已初始化
2025-08-05 09:38:48 - INFO - XML模板集成阶段已初始化
2025-08-05 09:38:48 - INFO - 开始解析配置文件
2025-08-05 09:38:48 - INFO - 成功读取文件，共 610 行
2025-08-05 09:38:48 - INFO - 配置文件读取成功，共 610 行
2025-08-05 09:38:48 - INFO - fortigate.detected_transparent_mode_from_header
2025-08-05 09:38:48 - INFO - 开始解析系统全局配置部分
2025-08-05 09:38:48 - INFO - fortigate.set_admin_sport
2025-08-05 09:38:48 - INFO - fortigate.set_admin_ssh_port
2025-08-05 09:38:48 - INFO - fortigate.set_hostname
2025-08-05 09:38:48 - INFO - fortigate.set_timezone
2025-08-05 09:38:48 - INFO - fortigate.system_settings_section_end
2025-08-05 09:38:48 - INFO - 未知 配置部分结束
2025-08-05 09:38:48 - INFO - 未知 配置部分结束
2025-08-05 09:38:48 - INFO - 未知 配置部分结束
2025-08-05 09:38:48 - INFO - 开始解析接口配置部分
2025-08-05 09:38:48 - INFO - 解析接口: ha
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 10
2025-08-05 09:38:48 - INFO - 完成接口 ha 解析
2025-08-05 09:38:48 - INFO - 解析接口: mgmt
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ***********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 9
2025-08-05 09:38:48 - INFO - 完成接口 mgmt 解析
2025-08-05 09:38:48 - INFO - 解析接口: port1
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 2
2025-08-05 09:38:48 - INFO - 完成接口 port1 解析
2025-08-05 09:38:48 - INFO - 解析接口: port2
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ***********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 12
2025-08-05 09:38:48 - INFO - 完成接口 port2 解析
2025-08-05 09:38:48 - INFO - 解析接口: port3
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): **********/16
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 19
2025-08-05 09:38:48 - INFO - 完成接口 port3 解析
2025-08-05 09:38:48 - INFO - 解析接口: port4
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 14
2025-08-05 09:38:48 - INFO - 完成接口 port4 解析
2025-08-05 09:38:48 - INFO - 解析接口: port5
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *************/30
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 15
2025-08-05 09:38:48 - INFO - 完成接口 port5 解析
2025-08-05 09:38:48 - INFO - 解析接口: port6
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ***********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 8
2025-08-05 09:38:48 - INFO - 完成接口 port6 解析
2025-08-05 09:38:48 - INFO - 解析接口: port7
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 7
2025-08-05 09:38:48 - INFO - 完成接口 port7 解析
2025-08-05 09:38:48 - INFO - 解析接口: port8
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 6
2025-08-05 09:38:48 - INFO - 完成接口 port8 解析
2025-08-05 09:38:48 - INFO - 解析接口: port9
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ***********/26
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口状态: down
2025-08-05 09:38:48 - INFO - 设置接口启用状态: False
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置接口角色: wan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 5
2025-08-05 09:38:48 - INFO - 完成接口 port9 解析
2025-08-05 09:38:48 - INFO - 解析接口: port10
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 3
2025-08-05 09:38:48 - INFO - 完成接口 port10 解析
2025-08-05 09:38:48 - INFO - 解析接口: port11
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 4
2025-08-05 09:38:48 - INFO - 完成接口 port11 解析
2025-08-05 09:38:48 - INFO - 解析接口: port12
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 20
2025-08-05 09:38:48 - INFO - 完成接口 port12 解析
2025-08-05 09:38:48 - INFO - 解析接口: s1
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 1
2025-08-05 09:38:48 - INFO - 完成接口 s1 解析
2025-08-05 09:38:48 - INFO - 解析接口: s2
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 16
2025-08-05 09:38:48 - INFO - 完成接口 s2 解析
2025-08-05 09:38:48 - INFO - 解析接口: vw1
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 17
2025-08-05 09:38:48 - INFO - 完成接口 vw1 解析
2025-08-05 09:38:48 - INFO - 解析接口: vw2
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 18
2025-08-05 09:38:48 - INFO - 完成接口 vw2 解析
2025-08-05 09:38:48 - INFO - 解析接口: x1
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *************/26
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置接口角色: wan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 13
2025-08-05 09:38:48 - WARNING - 无效的MTU格式: set mtu-override enable
2025-08-05 09:38:48 - INFO - 完成接口 x1 解析
2025-08-05 09:38:48 - INFO - 解析接口: x2
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ***********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping', 'https', 'ssh', 'snmp']
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 11
2025-08-05 09:38:48 - INFO - 完成接口 x2 解析
2025-08-05 09:38:48 - INFO - 解析接口: modem
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口模式: pppoe
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['fabric']
2025-08-05 09:38:48 - INFO - 设置接口状态: down
2025-08-05 09:38:48 - INFO - 设置接口启用状态: False
2025-08-05 09:38:48 - INFO - 设置接口类型: physical
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 37
2025-08-05 09:38:48 - INFO - 设置默认网关: disable
2025-08-05 09:38:48 - WARNING - 警告: 接口 'modem' 设置了不支持的defaultgw disable选项
2025-08-05 09:38:48 - INFO - 完成接口 modem 解析
2025-08-05 09:38:48 - INFO - 解析接口: naf.root
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: tunnel
2025-08-05 09:38:48 - INFO - 信息: 接口 'naf.root' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 55
2025-08-05 09:38:48 - INFO - 完成接口 naf.root 解析
2025-08-05 09:38:48 - INFO - 解析接口: l2t.root
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口类型: tunnel
2025-08-05 09:38:48 - INFO - 信息: 接口 'l2t.root' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 56
2025-08-05 09:38:48 - INFO - 完成接口 l2t.root 解析
2025-08-05 09:38:48 - INFO - 解析接口: ssl.root
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['fabric']
2025-08-05 09:38:48 - INFO - 设置接口类型: tunnel
2025-08-05 09:38:48 - INFO - 信息: 接口 'ssl.root' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 38
2025-08-05 09:38:48 - INFO - 完成接口 ssl.root 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vodafone_MPLS
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ***********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 21
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: port1
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 3452
2025-08-05 09:38:48 - INFO - 完成接口 Vodafone_MPLS 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan55
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): **********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 22
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 55
2025-08-05 09:38:48 - INFO - 完成接口 Vlan55 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan99
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 23
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 99
2025-08-05 09:38:48 - INFO - 完成接口 Vlan99 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan100
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): **********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 24
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 100
2025-08-05 09:38:48 - INFO - 完成接口 Vlan100 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan103
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 25
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 103
2025-08-05 09:38:48 - INFO - 完成接口 Vlan103 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan104
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 26
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 104
2025-08-05 09:38:48 - INFO - 完成接口 Vlan104 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan105
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 27
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 105
2025-08-05 09:38:48 - INFO - 完成接口 Vlan105 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan106
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 28
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 106
2025-08-05 09:38:48 - INFO - 完成接口 Vlan106 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan107
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 29
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 107
2025-08-05 09:38:48 - INFO - 完成接口 Vlan107 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan108
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 30
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 108
2025-08-05 09:38:48 - INFO - 完成接口 Vlan108 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan109
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 31
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 109
2025-08-05 09:38:48 - INFO - 完成接口 Vlan109 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan110
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 32
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 110
2025-08-05 09:38:48 - INFO - 完成接口 Vlan110 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan111
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 33
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 111
2025-08-05 09:38:48 - INFO - 完成接口 Vlan111 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan113
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 34
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 113
2025-08-05 09:38:48 - INFO - 完成接口 Vlan113 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan115
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 35
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 115
2025-08-05 09:38:48 - INFO - 完成接口 Vlan115 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan116
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 36
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 116
2025-08-05 09:38:48 - INFO - 完成接口 Vlan116 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan117
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *********/19
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping', 'snmp', 'radius-acct', 'fabric']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 39
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 117
2025-08-05 09:38:48 - INFO - 完成接口 Vlan117 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan120
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *********/23
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 40
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 120
2025-08-05 09:38:48 - INFO - 完成接口 Vlan120 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan136
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *********/22
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 41
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 136
2025-08-05 09:38:48 - INFO - 完成接口 Vlan136 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan150
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping', 'ssh', 'snmp', 'radius-acct']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 43
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 150
2025-08-05 09:38:48 - INFO - 完成接口 Vlan150 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan151
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 44
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 151
2025-08-05 09:38:48 - INFO - 完成接口 Vlan151 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan300
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): *********/22
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 45
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 300
2025-08-05 09:38:48 - INFO - 完成接口 Vlan300 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan130
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): **********/16
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:48 - INFO - 设置SNMP索引: 47
2025-08-05 09:38:48 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:48 - INFO - 设置子接口VLAN ID: 130
2025-08-05 09:38:48 - INFO - 完成接口 Vlan130 解析
2025-08-05 09:38:48 - INFO - 解析接口: Vlan155
2025-08-05 09:38:48 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:48 - INFO - 设置接口IP (掩码): **********/24
2025-08-05 09:38:48 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:38:48 - INFO - 设置接口角色: lan
2025-08-05 09:38:49 - INFO - 设置SNMP索引: 48
2025-08-05 09:38:49 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:49 - INFO - 设置子接口VLAN ID: 155
2025-08-05 09:38:49 - INFO - 完成接口 Vlan155 解析
2025-08-05 09:38:49 - INFO - 解析接口: Vlan200
2025-08-05 09:38:49 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:49 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:49 - INFO - 设置接口角色: lan
2025-08-05 09:38:49 - INFO - 设置SNMP索引: 49
2025-08-05 09:38:49 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:49 - INFO - 设置子接口VLAN ID: 200
2025-08-05 09:38:49 - INFO - 完成接口 Vlan200 解析
2025-08-05 09:38:49 - INFO - 解析接口: Vlan222
2025-08-05 09:38:49 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:49 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:49 - INFO - 设置接口角色: lan
2025-08-05 09:38:49 - INFO - 设置SNMP索引: 50
2025-08-05 09:38:49 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:49 - INFO - 设置子接口VLAN ID: 222
2025-08-05 09:38:49 - INFO - 完成接口 Vlan222 解析
2025-08-05 09:38:49 - INFO - 解析接口: VLAN101
2025-08-05 09:38:49 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:49 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:38:49 - INFO - 设置接口访问控制: ['ping', 'https', 'ssh', 'snmp', 'http', 'radius-acct', 'fabric']
2025-08-05 09:38:49 - INFO - 设置接口角色: lan
2025-08-05 09:38:49 - INFO - 设置SNMP索引: 52
2025-08-05 09:38:49 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:49 - INFO - 设置子接口VLAN ID: 101
2025-08-05 09:38:49 - INFO - 完成接口 VLAN101 解析
2025-08-05 09:38:49 - INFO - 解析接口: UITSEC_Forti
2025-08-05 09:38:49 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:49 - INFO - 设置接口类型: tunnel
2025-08-05 09:38:49 - INFO - 信息: 接口 'UITSEC_Forti' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:38:49 - INFO - 设置SNMP索引: 51
2025-08-05 09:38:49 - INFO - 设置子接口所属的物理接口: x1
2025-08-05 09:38:49 - INFO - 完成接口 UITSEC_Forti 解析
2025-08-05 09:38:49 - INFO - 解析接口: UITSEC_CP
2025-08-05 09:38:49 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:49 - INFO - 设置接口类型: tunnel
2025-08-05 09:38:49 - INFO - 信息: 接口 'UITSEC_CP' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:38:49 - INFO - 设置SNMP索引: 53
2025-08-05 09:38:49 - INFO - 设置子接口所属的物理接口: x1
2025-08-05 09:38:49 - INFO - 完成接口 UITSEC_CP 解析
2025-08-05 09:38:49 - INFO - 解析接口: BBS_PAM
2025-08-05 09:38:49 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:49 - INFO - 设置接口类型: tunnel
2025-08-05 09:38:49 - INFO - 信息: 接口 'BBS_PAM' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:38:49 - INFO - 设置SNMP索引: 54
2025-08-05 09:38:49 - INFO - 设置子接口所属的物理接口: x1
2025-08-05 09:38:49 - INFO - 完成接口 BBS_PAM 解析
2025-08-05 09:38:49 - INFO - 解析接口: BBS_PAM1
2025-08-05 09:38:49 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:49 - INFO - 设置接口类型: tunnel
2025-08-05 09:38:49 - INFO - 信息: 接口 'BBS_PAM1' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:38:49 - INFO - 设置SNMP索引: 57
2025-08-05 09:38:49 - INFO - 设置子接口所属的物理接口: x1
2025-08-05 09:38:49 - INFO - 完成接口 BBS_PAM1 解析
2025-08-05 09:38:49 - INFO - 解析接口: TO-CHECKPOINT
2025-08-05 09:38:49 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:49 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:38:49 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:49 - INFO - 设置接口角色: lan
2025-08-05 09:38:49 - INFO - 设置SNMP索引: 58
2025-08-05 09:38:49 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:49 - INFO - 设置子接口VLAN ID: 145
2025-08-05 09:38:49 - INFO - 完成接口 TO-CHECKPOINT 解析
2025-08-05 09:38:49 - INFO - 解析接口: Vlan114
2025-08-05 09:38:49 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:49 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:38:49 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:38:49 - INFO - 设置接口角色: lan
2025-08-05 09:38:49 - INFO - 设置SNMP索引: 46
2025-08-05 09:38:49 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:49 - INFO - 设置子接口VLAN ID: 114
2025-08-05 09:38:49 - INFO - 完成接口 Vlan114 解析
2025-08-05 09:38:49 - INFO - 解析接口: RUIJIE-MGMT
2025-08-05 09:38:49 - INFO - 设置接口虚拟域: root
2025-08-05 09:38:49 - INFO - 设置接口IP (掩码): *************/23
2025-08-05 09:38:49 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:38:49 - INFO - 设置接口角色: lan
2025-08-05 09:38:49 - INFO - 设置SNMP索引: 42
2025-08-05 09:38:49 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:38:49 - INFO - 设置子接口VLAN ID: 199
2025-08-05 09:38:49 - INFO - 完成接口 RUIJIE-MGMT 解析
2025-08-05 09:38:49 - INFO - 接口配置部分结束
2025-08-05 09:38:49 - INFO - 解析完成，找到 58 个接口, 0 个静态路由, 0 个区域, 0 个地址对象, 0 个地址组, 0 个服务对象, 0 个服务组
2025-08-05 09:38:49 - INFO - 解析完成，找到 58 个接口, 0 个静态路由, 0 个区域, 0 个地址对象, 0 个地址组, 0 个服务对象, 0 个服务组
2025-08-05 09:38:49 - INFO - Interface mapping loaded
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: 'mgmt': 'Ge0/0', 'port2': 'Ge0/2', 'port3': 'Ge0/3', 'port5': 'Ge0/5', 'port6': 'Ge0/6', 'port9': 'Ge0/1', 'x1': 'TenGe0/0'
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: 'mgmt'
2025-08-05 09:38:49 - INFO - 加载的接口映射: {'mgmt': 'Ge0/0', 'port2': 'Ge0/2', 'port3': 'Ge0/3', 'port5': 'Ge0/5', 'port6': 'Ge0/6', 'port9': 'Ge0/1', 'x1': 'TenGe0/0'}
2025-08-05 09:38:49 - INFO - 配置管理器：检测到开发环境
2025-08-05 09:38:49 - INFO - 配置管理器已初始化
2025-08-05 09:38:49 - INFO - YANG管理器已初始化
2025-08-05 09:38:49 - INFO - 验证服务已初始化
2025-08-05 09:38:49 - INFO - 接口映射验证通过
2025-08-05 09:38:49 - INFO - 接口映射验证通过
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: initial_data.get('config_file', 'unknown')
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: initial_data
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: initial_data.get('vendor', 'unknown')
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: initial_data
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: initial_data.get('model', 'unknown')
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: initial_data
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: initial_data.get('version', 'unknown')
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: initial_data
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: '存在' if initial_data.get('interface_mapping') else '不存在'
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: '存在' if initial_data
2025-08-05 09:38:49 - INFO - 阶段开始: fortigate_conversion, stage count: 12
2025-08-05 09:38:49 - INFO - 阶段开始: fortigate_conversion -> operation_mode (1/12)
2025-08-05 09:38:49 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:49 - INFO - 开始检测操作模式
2025-08-05 09:38:49 - INFO - Operation mode analysis: explicit=nat, role_interfaces=True, mgmt_interface=True, manageip=False
2025-08-05 09:38:49 - INFO - Route mode detected from explicit configuration: nat
2025-08-05 09:38:49 - INFO - 处理路由模式配置
2025-08-05 09:38:49 - INFO - 路由模式处理完成，主机名: KHU-FGT-1
2025-08-05 09:38:49 - INFO - 操作模式检测完成，模式: route，主机名: KHU-FGT-1
2025-08-05 09:38:49 - INFO - 管道阶段：阶段完成
2025-08-05 09:38:49 - INFO - 阶段开始: fortigate_conversion -> interface_processing (2/12)
2025-08-05 09:38:49 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:49 - INFO - 开始处理接口配置
2025-08-05 09:38:49 - INFO - 开始接口处理，共58个项目
2025-08-05 09:38:49 - WARNING - 接口 'ha' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'port1' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'port4' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'port7' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'port8' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'port10' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'port11' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'port12' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 's1' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 's2' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'vw1' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'vw2' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'x2' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - INFO - 过滤modem接口: modem
2025-08-05 09:38:49 - WARNING - 接口 'naf.root' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'l2t.root' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'ssl.root' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vodafone_MPLS' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan55' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan99' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan100' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan103' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan104' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan105' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan106' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan107' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan108' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan109' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan110' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan111' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan113' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan115' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan116' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan117' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan120' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan136' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan150' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan151' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan300' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan130' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan155' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan200' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan222' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'VLAN101' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'UITSEC_Forti' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'UITSEC_CP' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'BBS_PAM' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'BBS_PAM1' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'TO-CHECKPOINT' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'Vlan114' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 接口 'RUIJIE-MGMT' 警告：缺少接口映射
2025-08-05 09:38:49 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: 'mgmt': 'Ge0/0', 'port2': 'Ge0/2', 'port3': 'Ge0/3', 'port5': 'Ge0/5', 'port6': 'Ge0/6', 'port9': 'Ge0/1', 'x1': 'TenGe0/0'
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: 'mgmt'
2025-08-05 09:38:49 - INFO - DEBUG: 开始生成XML片段，converted接口数量: 7
2025-08-05 09:38:49 - INFO - DEBUG: physical_interfaces数量: 7
2025-08-05 09:38:49 - INFO - DEBUG: vlan_interfaces数量: 0
2025-08-05 09:38:49 - INFO - DEBUG: XML片段生成 - converted_interfaces: 7
2025-08-05 09:38:49 - INFO - DEBUG: XML片段生成 - vlan_interfaces: 0
2025-08-05 09:38:49 - INFO - DEBUG: XML片段生成 - physical_interfaces: 7
2025-08-05 09:38:49 - INFO - PhysicalInterfaceHandler初始化 - 非透明模式，transparent_mode_result保持None
2025-08-05 09:38:49 - INFO - DEBUG: 开始处理物理接口，数量: 7
2025-08-05 09:38:49 - INFO - DEBUG: 处理物理接口 mgmt, is_subinterface: False
2025-08-05 09:38:49 - INFO - DEBUG: 为接口 mgmt 生成XML
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:38:49 - INFO - interface handler: create new interface
2025-08-05 09:38:49 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:38:49 - INFO - interface handler: create fortinet interface
2025-08-05 09:38:49 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:38:49 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:38:49 - INFO - interface handler: configured static ip
2025-08-05 09:38:49 - INFO - DEBUG: 最终的combined_access: ['https', 'ping']
2025-08-05 09:38:49 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: false
2025-08-05 09:38:49 - INFO - interface handler: configured access control
2025-08-05 09:38:49 - INFO - interface handler: fortinet interface created
2025-08-05 09:38:49 - INFO - DEBUG: 接口 mgmt XML生成完成
2025-08-05 09:38:49 - INFO - DEBUG: 处理物理接口 port2, is_subinterface: False
2025-08-05 09:38:49 - INFO - DEBUG: 为接口 port2 生成XML
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:38:49 - INFO - interface handler: create new interface
2025-08-05 09:38:49 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:38:49 - INFO - interface handler: create fortinet interface
2025-08-05 09:38:49 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:38:49 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:38:49 - INFO - interface handler: configured static ip
2025-08-05 09:38:49 - INFO - DEBUG: 最终的combined_access: ['https', 'ping']
2025-08-05 09:38:49 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: false
2025-08-05 09:38:49 - INFO - interface handler: configured access control
2025-08-05 09:38:49 - INFO - interface handler: fortinet interface created
2025-08-05 09:38:49 - INFO - DEBUG: 接口 port2 XML生成完成
2025-08-05 09:38:49 - INFO - DEBUG: 处理物理接口 port3, is_subinterface: False
2025-08-05 09:38:49 - INFO - DEBUG: 为接口 port3 生成XML
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:38:49 - INFO - interface handler: create new interface
2025-08-05 09:38:49 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:38:49 - INFO - interface handler: create fortinet interface
2025-08-05 09:38:49 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:38:49 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:38:49 - INFO - interface handler: configured static ip
2025-08-05 09:38:49 - INFO - DEBUG: 最终的combined_access: ['ping']
2025-08-05 09:38:49 - INFO - DEBUG: 最终XML值 - https: false, ping: true, ssh: false
2025-08-05 09:38:49 - INFO - interface handler: configured access control
2025-08-05 09:38:49 - INFO - interface handler: fortinet interface created
2025-08-05 09:38:49 - INFO - DEBUG: 接口 port3 XML生成完成
2025-08-05 09:38:49 - INFO - DEBUG: 处理物理接口 port5, is_subinterface: False
2025-08-05 09:38:49 - INFO - DEBUG: 为接口 port5 生成XML
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:38:49 - INFO - interface handler: create new interface
2025-08-05 09:38:49 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:38:49 - INFO - interface handler: create fortinet interface
2025-08-05 09:38:49 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:38:49 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:38:49 - INFO - interface handler: configured static ip
2025-08-05 09:38:49 - INFO - DEBUG: 最终的combined_access: ['https', 'ping']
2025-08-05 09:38:49 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: false
2025-08-05 09:38:49 - INFO - interface handler: configured access control
2025-08-05 09:38:49 - INFO - interface handler: fortinet interface created
2025-08-05 09:38:49 - INFO - DEBUG: 接口 port5 XML生成完成
2025-08-05 09:38:49 - INFO - DEBUG: 处理物理接口 port6, is_subinterface: False
2025-08-05 09:38:49 - INFO - DEBUG: 为接口 port6 生成XML
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:38:49 - INFO - interface handler: create new interface
2025-08-05 09:38:49 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:38:49 - INFO - interface handler: create fortinet interface
2025-08-05 09:38:49 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:38:49 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:38:49 - INFO - interface handler: configured static ip
2025-08-05 09:38:49 - INFO - DEBUG: 最终的combined_access: ['https', 'ping']
2025-08-05 09:38:49 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: false
2025-08-05 09:38:49 - INFO - interface handler: configured access control
2025-08-05 09:38:49 - INFO - interface handler: fortinet interface created
2025-08-05 09:38:49 - INFO - DEBUG: 接口 port6 XML生成完成
2025-08-05 09:38:49 - INFO - DEBUG: 处理物理接口 port9, is_subinterface: False
2025-08-05 09:38:49 - INFO - DEBUG: 为接口 port9 生成XML
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:38:49 - INFO - interface handler: create new interface
2025-08-05 09:38:49 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:38:49 - INFO - interface handler: create fortinet interface
2025-08-05 09:38:49 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:38:49 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:38:49 - INFO - interface handler: configured static ip
2025-08-05 09:38:49 - INFO - DEBUG: 最终的combined_access: ['ping']
2025-08-05 09:38:49 - INFO - DEBUG: 最终XML值 - https: false, ping: true, ssh: false
2025-08-05 09:38:49 - INFO - interface handler: configured access control
2025-08-05 09:38:49 - INFO - interface handler: fortinet interface created
2025-08-05 09:38:49 - INFO - DEBUG: 接口 port9 XML生成完成
2025-08-05 09:38:49 - INFO - DEBUG: 处理物理接口 x1, is_subinterface: False
2025-08-05 09:38:49 - INFO - DEBUG: 为接口 x1 生成XML
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:38:49 - INFO - interface handler: create new interface
2025-08-05 09:38:49 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:38:49 - INFO - interface handler: create fortinet interface
2025-08-05 09:38:49 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:38:49 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:38:49 - INFO - interface handler: configured static ip
2025-08-05 09:38:49 - INFO - DEBUG: 最终的combined_access: ['ping']
2025-08-05 09:38:49 - INFO - DEBUG: 最终XML值 - https: false, ping: true, ssh: false
2025-08-05 09:38:49 - INFO - interface handler: configured access control
2025-08-05 09:38:49 - INFO - interface handler: fortinet interface created
2025-08-05 09:38:49 - INFO - DEBUG: 接口 x1 XML生成完成
2025-08-05 09:38:49 - INFO - DEBUG: XML片段生成完成，长度: 11785
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: 'mgmt': 'Ge0/0', 'port2': 'Ge0/2', 'port3': 'Ge0/3', 'port5': 'Ge0/5', 'port6': 'Ge0/6', 'port9': 'Ge0/1', 'x1': 'TenGe0/0'
2025-08-05 09:38:49 - WARNING - 消息中缺少参数: 'mgmt'
2025-08-05 09:38:49 - INFO - DEBUG: 接口映射 1: mgmt -> Ge0/0 (类型: physical)
2025-08-05 09:38:49 - INFO - DEBUG: 接口映射 2: port2 -> Ge0/2 (类型: physical)
2025-08-05 09:38:49 - INFO - DEBUG: 接口映射 3: port3 -> Ge0/3 (类型: physical)
2025-08-05 09:38:49 - INFO - DEBUG: 接口映射 4: port5 -> Ge0/5 (类型: physical)
2025-08-05 09:38:49 - INFO - DEBUG: 接口映射 5: port6 -> Ge0/6 (类型: physical)
2025-08-05 09:38:49 - INFO - DEBUG: 接口映射 6: port9 -> Ge0/1 (类型: physical)
2025-08-05 09:38:49 - INFO - DEBUG: 接口映射 7: x1 -> TenGe0/0 (类型: physical)
2025-08-05 09:38:50 - INFO - 最终接口映射已创建
2025-08-05 09:38:50 - INFO - 接口映射已保存
2025-08-05 09:38:50 - INFO - 最终接口映射已保存
2025-08-05 09:38:50 - INFO - [待翻译] interface_processing_stage.final_mapping_saved
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: 'mgmt': 'Ge0/0', 'port2': 'Ge0/2', 'port3': 'Ge0/3', 'port5': 'Ge0/5', 'port6': 'Ge0/6', 'port9': 'Ge0/1', 'x1': 'TenGe0/0'
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: 'mgmt'
2025-08-05 09:38:50 - INFO - DEBUG: 保存的映射内容: {'mgmt': 'Ge0/0', 'port2': 'Ge0/2', 'port3': 'Ge0/3', 'port5': 'Ge0/5', 'port6': 'Ge0/6', 'port9': 'Ge0/1', 'x1': 'TenGe0/0'}
2025-08-05 09:38:50 - INFO - 接口处理完成：成功转换7/58，耗时886ms
2025-08-05 09:38:50 - INFO - 接口处理：51个项目被跳过
2025-08-05 09:38:50 - INFO - 接口处理完成，转换: 7，安全区域: 1
2025-08-05 09:38:50 - INFO - 管道阶段：阶段完成
2025-08-05 09:38:50 - INFO - 阶段开始: fortigate_conversion -> service_processing (3/12)
2025-08-05 09:38:50 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:50 - INFO - 开始服务对象处理
2025-08-05 09:38:50 - INFO - 没有找到服务对象配置
2025-08-05 09:38:50 - INFO - 开始服务对象处理
2025-08-05 09:38:50 - INFO - 服务对象处理完成，耗时15ms
2025-08-05 09:38:50 - INFO - 管道阶段：阶段完成
2025-08-05 09:38:50 - INFO - 阶段开始: fortigate_conversion -> address_processing (4/12)
2025-08-05 09:38:50 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:50 - INFO - 开始处理地址对象
2025-08-05 09:38:50 - INFO - 发现 0 个动态生成的地址对象
2025-08-05 09:38:50 - INFO - 地址对象统计: 原始=0, VIP=0, 动态生成=0, 总计=0
2025-08-05 09:38:50 - INFO - 没有地址对象需要处理
2025-08-05 09:38:50 - INFO - 开始地址对象处理
2025-08-05 09:38:50 - INFO - 地址对象处理完成，耗时6ms
2025-08-05 09:38:50 - INFO - 管道阶段：阶段完成
2025-08-05 09:38:50 - INFO - 阶段开始: fortigate_conversion -> address_group_processing (5/12)
2025-08-05 09:38:50 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:50 - INFO - 开始处理地址对象组
2025-08-05 09:38:50 - INFO - 没有地址对象组需要处理
2025-08-05 09:38:50 - INFO - 管道阶段：阶段完成
2025-08-05 09:38:50 - INFO - 阶段开始: fortigate_conversion -> service_group_processing (6/12)
2025-08-05 09:38:50 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:50 - INFO - 开始处理服务对象组
2025-08-05 09:38:50 - INFO - 没有服务对象组需要处理
2025-08-05 09:38:50 - INFO - 管道阶段：阶段完成
2025-08-05 09:38:50 - INFO - 阶段开始: fortigate_conversion -> zone_processing (7/12)
2025-08-05 09:38:50 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:50 - INFO - 开始处理安全区域
2025-08-05 09:38:50 - INFO - Using interface mapping from context: 7 mappings
2025-08-05 09:38:50 - INFO - Interface mapping set directly: 7 mappings
2025-08-05 09:38:50 - INFO - 找到 0 个区域配置，操作模式: route
2025-08-05 09:38:50 - INFO - Interface mapping loaded
2025-08-05 09:38:50 - INFO - Interface mapping loaded (flat format): 7 mappings
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: 'trust', 'untrust'
2025-08-05 09:38:50 - WARNING - Zone skipped: trust - 区域的所有接口都未配置映射关系或区域没有配置接口
2025-08-05 09:38:50 - WARNING - Zone skipped: untrust - 区域的所有接口都未配置映射关系或区域没有配置接口
2025-08-05 09:38:50 - INFO - 区域处理完成: 转换成功 0/0
2025-08-05 09:38:50 - INFO - 管道阶段：阶段完成
2025-08-05 09:38:50 - INFO - 阶段开始: fortigate_conversion -> time_range_processing (8/12)
2025-08-05 09:38:50 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:50 - INFO - 开始处理时间对象
2025-08-05 09:38:50 - INFO - 找到 0 个时间对象
2025-08-05 09:38:50 - INFO - 没有时间对象需要处理
2025-08-05 09:38:50 - INFO - 管道阶段：阶段完成
2025-08-05 09:38:50 - INFO - 阶段开始: fortigate_conversion -> dns_processing (9/12)
2025-08-05 09:38:50 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:50 - INFO - 开始DNS处理
2025-08-05 09:38:50 - INFO - 没有配置DNS服务器
2025-08-05 09:38:50 - INFO - DNS配置处理完成: DNS服务器 0 个，静态域名 0 个
2025-08-05 09:38:50 - INFO - DNS处理完成: DNS服务器 0 个，静态域名 0 个
2025-08-05 09:38:50 - INFO - 管道阶段：阶段完成
2025-08-05 09:38:50 - INFO - 阶段开始: fortigate_conversion -> static_route_processing (10/12)
2025-08-05 09:38:50 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:50 - INFO - 开始静态路由处理
2025-08-05 09:38:50 - INFO - 检测到列表格式的静态路由数据，转换为字典格式
2025-08-05 09:38:50 - INFO - 转换后找到 0 个静态路由
2025-08-05 09:38:50 - INFO - 没有静态路由需要处理
2025-08-05 09:38:50 - INFO - 管道阶段：阶段完成
2025-08-05 09:38:50 - INFO - 阶段开始: fortigate_conversion -> fortigate_policy_conversion (11/12)
2025-08-05 09:38:50 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:50 - INFO - Fortigate策略阶段：开始处理
2025-08-05 09:38:50 - INFO - fortigate_strategy.ntos_version
2025-08-05 09:38:50 - INFO - DEBUG: config_data keys: ['interfaces', 'static_routes', 'static_routes_ipv6', 'zones', 'address_objects', 'address_groups', 'service_objects', 'service_groups', 'policies', 'nat_rules', 'ippools', 'time_ranges', 'vpn_configs', 'dhcp_configs', 'dns_config', 'auth_config', 'log_config', 'system_settings', 'warnings', 'filtered_pppoe_count']
2025-08-05 09:38:50 - INFO - DEBUG: policies type: <class 'list'>, count: 0
2025-08-05 09:38:50 - INFO - 策略转换数据准备：找到 0 个策略
2025-08-05 09:38:50 - INFO - fortigate_strategy.nat.rules_stored
2025-08-05 09:38:50 - INFO - fortigate_strategy.policy.processing_summary
2025-08-05 09:38:50 - INFO - fortigate strategy: conversion data prepared
2025-08-05 09:38:50 - INFO - fortigate_policy_stage.ippools_data_retrieved
2025-08-05 09:38:50 - INFO - Fortigate策略阶段：转换已完成
2025-08-05 09:38:50 - INFO - Fortigate策略阶段：XML集成数据已准备
2025-08-05 09:38:50 - INFO - Fortigate策略阶段：处理已完成
2025-08-05 09:38:50 - INFO - 管道阶段：阶段完成
2025-08-05 09:38:50 - INFO - 阶段开始: fortigate_conversion -> xml_template_integration (12/12)
2025-08-05 09:38:50 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:50 - INFO - xml template integration stage: start processing
2025-08-05 09:38:50 - INFO - 开始加载XML模板: model=z3200s, version=R11
2025-08-05 09:38:50 - INFO - XML模板加载成功: model=z3200s, version=R11
2025-08-05 09:38:50 - INFO - 系统配置集成完成，主机名: KHU-FGT-1，模式: route
2025-08-05 09:38:50 - INFO - XML集成器：接口工作模式集成完成，接口数量: 7
2025-08-05 09:38:50 - INFO - 找到现有bridge节点，数量: 4
2025-08-05 09:38:50 - INFO - bridge link-interface已清空，数量: 0
2025-08-05 09:38:50 - INFO - bridge link-interface已清空，数量: 9
2025-08-05 09:38:50 - INFO - bridge link-interface已清空，数量: 0
2025-08-05 09:38:50 - INFO - bridge link-interface已清空，数量: 0
2025-08-05 09:38:50 - INFO - 桥接配置集成完成
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn:ruijie:ntos, urn:ruijie:ntos:params:xml:ns:yang:system
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn
2025-08-05 09:38:50 - INFO - DEBUG: 开始集成接口XML片段
2025-08-05 09:38:50 - INFO - DEBUG: 查找现有interface节点结果: True
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn:ruijie:ntos:params:xml:ns:yang:interface
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn
2025-08-05 09:38:50 - INFO - DEBUG: 找到interface节点，标签: {urn:ruijie:ntos:params:xml:ns:yang:interface}interface, 命名空间: None
2025-08-05 09:38:50 - INFO - 找到现有的interface节点，开始合并
2025-08-05 09:38:50 - INFO - 找到 0 个VLAN接口元素
2025-08-05 09:38:50 - INFO - DEBUG: 收集到 10 个现有物理接口
2025-08-05 09:38:50 - INFO - DEBUG: 模板中现有物理接口数量: 10
2025-08-05 09:38:50 - INFO - DEBUG: XML片段中物理接口数量: 7
2025-08-05 09:38:50 - INFO - DEBUG: XML片段中的接口名称: []
2025-08-05 09:38:50 - INFO - DEBUG: 处理接口片段: Ge0/0
2025-08-05 09:38:50 - INFO - 更新现有物理接口: Ge0/0
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新现有接口: Ge0/0
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: enabled = true, 标签: enabled
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: working-mode = route, 标签: working-mode
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: wanlan = lan
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: wanlan = lan, 标签: wanlan
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: wanlan = lan
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: description = None, 标签: description
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新复杂字段: Ge0/0
2025-08-05 09:38:50 - INFO - 🔧 DEBUG: 开始更新IPv4配置
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新地址配置查找结果: True
2025-08-05 09:38:50 - INFO - xml_template_integration.new_dhcp_result
2025-08-05 09:38:50 - INFO - xml_template_integration.new_pppoe_result
2025-08-05 09:38:50 - INFO - ✅ DEBUG: 找到新IPv4地址配置，开始检查-修改-创建
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 现有IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔄 DEBUG: 发现现有IPv4配置，执行修改策略
2025-08-05 09:38:50 - INFO - 🔄 DEBUG: 更新现有地址配置
2025-08-05 09:38:50 - INFO - 🔄 DEBUG: 模板IP字段已更新: *************/24 -> ***********/24
2025-08-05 09:38:50 - INFO - ✅ DEBUG: IPv4配置处理完成，IP地址: ***********/24
2025-08-05 09:38:50 - INFO - 开始更新带宽配置
2025-08-05 09:38:50 - INFO - 带宽配置更新完成
2025-08-05 09:38:50 - INFO - DEBUG: 物理接口节点更新完成: Ge0/0
2025-08-05 09:38:50 - INFO - DEBUG: 处理接口片段: Ge0/2
2025-08-05 09:38:50 - INFO - 更新现有物理接口: Ge0/2
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新现有接口: Ge0/2
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: enabled = true, 标签: enabled
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: working-mode = bridge
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: working-mode = route, 标签: working-mode
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: wanlan = lan
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: wanlan = lan, 标签: wanlan
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: wanlan = lan
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: description = None, 标签: description
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新复杂字段: Ge0/2
2025-08-05 09:38:50 - INFO - 🔧 DEBUG: 开始更新IPv4配置
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新地址配置查找结果: True
2025-08-05 09:38:50 - INFO - xml_template_integration.new_dhcp_result
2025-08-05 09:38:50 - INFO - xml_template_integration.new_pppoe_result
2025-08-05 09:38:50 - INFO - ✅ DEBUG: 找到新IPv4地址配置，开始检查-修改-创建
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 现有IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔄 DEBUG: 发现现有IPv4配置，执行修改策略
2025-08-05 09:38:50 - INFO - ➕ DEBUG: 添加新地址配置到现有IPv4
2025-08-05 09:38:50 - INFO - ✅ DEBUG: IPv4配置处理完成，IP地址: ***********/24
2025-08-05 09:38:50 - INFO - 开始更新带宽配置
2025-08-05 09:38:50 - INFO - 带宽配置更新完成
2025-08-05 09:38:50 - INFO - DEBUG: 物理接口节点更新完成: Ge0/2
2025-08-05 09:38:50 - INFO - DEBUG: 处理接口片段: Ge0/3
2025-08-05 09:38:50 - INFO - 更新现有物理接口: Ge0/3
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新现有接口: Ge0/3
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: enabled = true, 标签: enabled
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: working-mode = bridge
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: working-mode = route, 标签: working-mode
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: wanlan = lan
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: wanlan = lan, 标签: wanlan
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: wanlan = lan
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: description = None, 标签: description
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新复杂字段: Ge0/3
2025-08-05 09:38:50 - INFO - 🔧 DEBUG: 开始更新IPv4配置
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新地址配置查找结果: True
2025-08-05 09:38:50 - INFO - xml_template_integration.new_dhcp_result
2025-08-05 09:38:50 - INFO - xml_template_integration.new_pppoe_result
2025-08-05 09:38:50 - INFO - ✅ DEBUG: 找到新IPv4地址配置，开始检查-修改-创建
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 现有IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔄 DEBUG: 发现现有IPv4配置，执行修改策略
2025-08-05 09:38:50 - INFO - ➕ DEBUG: 添加新地址配置到现有IPv4
2025-08-05 09:38:50 - INFO - ✅ DEBUG: IPv4配置处理完成，IP地址: **********/16
2025-08-05 09:38:50 - INFO - 开始更新带宽配置
2025-08-05 09:38:50 - INFO - 带宽配置更新完成
2025-08-05 09:38:50 - INFO - DEBUG: 物理接口节点更新完成: Ge0/3
2025-08-05 09:38:50 - INFO - DEBUG: 处理接口片段: Ge0/5
2025-08-05 09:38:50 - INFO - 更新现有物理接口: Ge0/5
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新现有接口: Ge0/5
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: enabled = true, 标签: enabled
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: working-mode = bridge
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: working-mode = route, 标签: working-mode
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: wanlan = lan
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: wanlan = lan, 标签: wanlan
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: wanlan = lan
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: description = None, 标签: description
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新复杂字段: Ge0/5
2025-08-05 09:38:50 - INFO - 🔧 DEBUG: 开始更新IPv4配置
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新地址配置查找结果: True
2025-08-05 09:38:50 - INFO - xml_template_integration.new_dhcp_result
2025-08-05 09:38:50 - INFO - xml_template_integration.new_pppoe_result
2025-08-05 09:38:50 - INFO - ✅ DEBUG: 找到新IPv4地址配置，开始检查-修改-创建
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 现有IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔄 DEBUG: 发现现有IPv4配置，执行修改策略
2025-08-05 09:38:50 - INFO - ➕ DEBUG: 添加新地址配置到现有IPv4
2025-08-05 09:38:50 - INFO - ✅ DEBUG: IPv4配置处理完成，IP地址: *************/30
2025-08-05 09:38:50 - INFO - 开始更新带宽配置
2025-08-05 09:38:50 - INFO - 带宽配置更新完成
2025-08-05 09:38:50 - INFO - DEBUG: 物理接口节点更新完成: Ge0/5
2025-08-05 09:38:50 - INFO - DEBUG: 处理接口片段: Ge0/6
2025-08-05 09:38:50 - INFO - 更新现有物理接口: Ge0/6
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新现有接口: Ge0/6
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: enabled = true, 标签: enabled
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: working-mode = bridge
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: working-mode = route, 标签: working-mode
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: wanlan = lan
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: wanlan = lan, 标签: wanlan
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: wanlan = lan
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: description = None, 标签: description
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新复杂字段: Ge0/6
2025-08-05 09:38:50 - INFO - 🔧 DEBUG: 开始更新IPv4配置
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新地址配置查找结果: True
2025-08-05 09:38:50 - INFO - xml_template_integration.new_dhcp_result
2025-08-05 09:38:50 - INFO - xml_template_integration.new_pppoe_result
2025-08-05 09:38:50 - INFO - ✅ DEBUG: 找到新IPv4地址配置，开始检查-修改-创建
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 现有IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔄 DEBUG: 发现现有IPv4配置，执行修改策略
2025-08-05 09:38:50 - INFO - ➕ DEBUG: 添加新地址配置到现有IPv4
2025-08-05 09:38:50 - INFO - ✅ DEBUG: IPv4配置处理完成，IP地址: ***********/24
2025-08-05 09:38:50 - INFO - 开始更新带宽配置
2025-08-05 09:38:50 - INFO - 带宽配置更新完成
2025-08-05 09:38:50 - INFO - DEBUG: 物理接口节点更新完成: Ge0/6
2025-08-05 09:38:50 - INFO - DEBUG: 处理接口片段: Ge0/1
2025-08-05 09:38:50 - INFO - 更新现有物理接口: Ge0/1
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新现有接口: Ge0/1
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: enabled = false
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: enabled = false, 标签: enabled
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: enabled = false
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: working-mode = bridge
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: working-mode = route, 标签: working-mode
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: wanlan = wan
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: wanlan = wan, 标签: wanlan
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: wanlan = wan
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: description = None, 标签: description
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新复杂字段: Ge0/1
2025-08-05 09:38:50 - INFO - 🔧 DEBUG: 开始更新IPv4配置
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新地址配置查找结果: True
2025-08-05 09:38:50 - INFO - xml_template_integration.new_dhcp_result
2025-08-05 09:38:50 - INFO - xml_template_integration.new_pppoe_result
2025-08-05 09:38:50 - INFO - ✅ DEBUG: 找到新IPv4地址配置，开始检查-修改-创建
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 现有IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔄 DEBUG: 发现现有IPv4配置，执行修改策略
2025-08-05 09:38:50 - INFO - ➕ DEBUG: 添加新地址配置到现有IPv4
2025-08-05 09:38:50 - INFO - ✅ DEBUG: IPv4配置处理完成，IP地址: ***********/26
2025-08-05 09:38:50 - INFO - 开始更新带宽配置
2025-08-05 09:38:50 - INFO - 带宽配置更新完成
2025-08-05 09:38:50 - INFO - DEBUG: 物理接口节点更新完成: Ge0/1
2025-08-05 09:38:50 - INFO - DEBUG: 处理接口片段: TenGe0/0
2025-08-05 09:38:50 - INFO - 更新现有物理接口: TenGe0/0
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新现有接口: TenGe0/0
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: enabled = true, 标签: enabled
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: enabled = true
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 删除现有字段: working-mode = bridge
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: working-mode = route, 标签: working-mode
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: working-mode = route
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: wanlan = wan
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: wanlan = wan, 标签: wanlan
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: wanlan = wan
2025-08-05 09:38:50 - INFO - DEBUG: 找到新字段: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 更新字段完成: description = None, 标签: description
2025-08-05 09:38:50 - INFO - DEBUG: 字段验证成功: description = None
2025-08-05 09:38:50 - INFO - DEBUG: 开始更新复杂字段: TenGe0/0
2025-08-05 09:38:50 - INFO - 🔧 DEBUG: 开始更新IPv4配置
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新地址配置查找结果: True
2025-08-05 09:38:50 - INFO - xml_template_integration.new_dhcp_result
2025-08-05 09:38:50 - INFO - xml_template_integration.new_pppoe_result
2025-08-05 09:38:50 - INFO - ✅ DEBUG: 找到新IPv4地址配置，开始检查-修改-创建
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 现有IPv4直接配置查找结果: True
2025-08-05 09:38:50 - INFO - 🔄 DEBUG: 发现现有IPv4配置，执行修改策略
2025-08-05 09:38:50 - INFO - ➕ DEBUG: 添加新地址配置到现有IPv4
2025-08-05 09:38:50 - INFO - ✅ DEBUG: IPv4配置处理完成，IP地址: *************/26
2025-08-05 09:38:50 - INFO - 开始更新带宽配置
2025-08-05 09:38:50 - INFO - 带宽配置更新完成
2025-08-05 09:38:50 - INFO - DEBUG: 物理接口节点更新完成: TenGe0/0
2025-08-05 09:38:50 - INFO - 跳过模板接口过滤，保留已更新的接口
2025-08-05 09:38:50 - INFO - 接口配置集成完成，接口数: 7，区域数: 0
2025-08-05 09:38:50 - INFO - 没有地址对象组XML片段
2025-08-05 09:38:50 - INFO - 没有服务对象组XML片段
2025-08-05 09:38:50 - INFO - 没有安全区域XML片段
2025-08-05 09:38:50 - INFO - 没有时间对象XML片段需要集成
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn:ruijie:ntos, urn:ruijie:ntos:params:xml:ns:yang:system
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn
2025-08-05 09:38:50 - INFO - DNS配置集成完成: DNS服务器 0 个，静态域名 0 个
2025-08-05 09:38:50 - INFO - 没有静态路由XML片段需要集成
2025-08-05 09:38:50 - INFO - 没有安全策略XML片段需要集成
2025-08-05 09:38:50 - INFO - 没有NAT XML片段需要集成
2025-08-05 09:38:50 - INFO - 修复后统计: Bridge模式=0, Route模式=10
2025-08-05 09:38:50 - INFO - 成功修复了 3 个接口的工作模式
2025-08-05 09:38:50 - WARNING - XML模板集成阶段：命名空间警告
2025-08-05 09:38:50 - WARNING - XML模板集成阶段：重复节点警告
2025-08-05 09:38:50 - WARNING - 发现 2 个重复的 security-zone 节点，保留第一个
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn:ruijie:ntos:params:xml:ns:yang:threat-intelligence
2025-08-05 09:38:50 - WARNING - 消息中缺少参数: urn
2025-08-05 09:38:50 - WARNING - 发现 2 个重复的 time-range 节点，保留第一个
2025-08-05 09:38:50 - INFO - XML优化：移除了 2 个重复的容器节点
2025-08-05 09:38:50 - INFO - xml template integration stage: xml validated optimized
2025-08-05 09:38:50 - INFO - xml_template_integration_stage.applying_yang_validation_fixes
2025-08-05 09:38:50 - INFO - 🔧 开始修复YANG模型验证问题
2025-08-05 09:38:50 - INFO - name_mapping_manager.references_updated
2025-08-05 09:38:50 - INFO - 📊 YANG验证修复统计:
2025-08-05 09:38:50 - INFO -   处理元素总数: 0
2025-08-05 09:38:50 - INFO -   修复缺失name键: 0
2025-08-05 09:38:50 - INFO -   清理无效名称: 0
2025-08-05 09:38:50 - INFO -   清理无效描述: 0
2025-08-05 09:38:50 - INFO - ✅ YANG模型验证问题修复完成
2025-08-05 09:38:50 - INFO - name_mapping_manager.references_updated
2025-08-05 09:38:50 - INFO - name_mapping_manager.report_generated
2025-08-05 09:38:50 - INFO - xml_template_integration_stage.yang_validation_fixes_applied
2025-08-05 09:38:50 - INFO - xml template integration stage: xml generated
2025-08-05 09:38:50 - INFO - [待翻译] xml_template_integration_stage.backup_xml_saved
2025-08-05 09:38:50 - INFO - [待翻译] xml_template_integration_stage.original_xml_saved
2025-08-05 09:38:50 - INFO - xml template integration stage: processing completed
2025-08-05 09:38:50 - INFO - 管道阶段：阶段完成
2025-08-05 09:38:50 - INFO - 阶段完成: fortigate_conversion, 耗时: 1.17秒
2025-08-05 09:38:50 - INFO - Pipeline execution completed: fortigate_conversion, state: completed, duration: 1.17s, errors: 0, warnings: 2
2025-08-05 09:38:50 - INFO - 🔍 DEBUG: 新架构管道执行完成，状态: completed
2025-08-05 09:38:50 - INFO - YANG验证阶段已初始化
2025-08-05 09:38:50 - INFO - 开始独立YANG验证
2025-08-05 09:38:50 - INFO - 管道阶段：阶段开始
2025-08-05 09:38:50 - INFO - 开始YANG验证处理
2025-08-05 09:39:24 - INFO - YANG验证已执行
2025-08-05 09:39:25 - WARNING - YANG验证报告生成失败
2025-08-05 09:39:25 - INFO - YANG验证报告已生成
2025-08-05 09:39:25 - INFO - YANG验证处理完成
2025-08-05 09:39:25 - INFO - 管道阶段：阶段完成
2025-08-05 09:39:25 - INFO - 独立YANG验证完成
2025-08-05 09:39:25 - WARNING - YANG验证失败，跳过加密处理
2025-08-05 09:39:25 - INFO - 详细转换统计
2025-08-05 09:39:25 - INFO - 操作模式检测：
  ✓ 检测到route模式
2025-08-05 09:39:25 - INFO - 接口转换：
  ✓ 成功转换: 7个
    - mgmt → Ge0/0
    - port2 → Ge0/2
    - port3 → Ge0/3
    - port5 → Ge0/5
    - port6 → Ge0/6
    - 以及其他2个
  ⚠ 跳过转换: 51个
    跳过详情:
      • ha: no_mapping
      • port1: no_mapping
      • port4: no_mapping
      • port7: no_mapping
      • port8: no_mapping
      • 以及其他46个
    建议: 这些接口类型暂不支持自动转换，请手动配置
2025-08-05 09:39:25 - INFO - 区域转换：
  ⚠ 跳过转换: 2个
    跳过详情:
      • trust: 区域的所有接口都未配置映射关系或区域没有配置接口
      • untrust: 区域的所有接口都未配置映射关系或区域没有配置接口
2025-08-05 09:39:25 - INFO - DNS转换：
  ✓ 处理完成
2025-08-05 09:39:25 - INFO - 策略规则转换：
  ✓ 处理完成
2025-08-05 09:39:25 - INFO - XML模板集成：
  ✓ 成功转换: 1个
2025-08-05 09:39:25 - INFO - === 综合转换报告 ===

源厂商: fortigate
目标型号: z3200s
目标版本: R11
转换耗时: 36.50 s

=== 详细转换统计 ===

无统计信息可用

=== 配置迁移后续步骤 ===

必须完成的配置：
1. 🔴 批量添加 50 个接口的映射关系
   - 原因: 以下接口缺少映射关系：ha, port1, port4, port7, port8
   - 操作步骤:
     • 检查接口映射文件的完整性
     • 批量添加缺失的接口映射
     • 验证映射关系的正确性

建议完成的优化：
1. 🟡 验证转换后的配置
   - 原因: 建议在部署前验证配置的正确性
   - 操作步骤:
     • 检查XML配置文件的语法
     • 在测试环境中验证策略规则
     • 确认所有功能正常工作

可选的改进：
1. 🟢 备份原始配置
   - 原因: 在部署新配置前备份原始配置
   - 操作步骤:
     • 导出当前设备配置
     • 保存配置文件到安全位置

=== 报告结束 ===
2025-08-05 09:39:25 - INFO - 正在尝试将XML保存到文件
2025-08-05 09:39:25 - INFO - 命名空间修复已应用
2025-08-05 09:39:25 - WARNING - 转换工作流程：后处理失败
2025-08-05 09:39:25 - INFO - 转换工作流程已成功完成
2025-08-05 09:39:25 - WARNING - YANG验证已完成（有错误）
2025-08-05 09:39:25 - INFO - 转换已完成
2025-08-05 09:39:25 - INFO - 转换成功完成！
